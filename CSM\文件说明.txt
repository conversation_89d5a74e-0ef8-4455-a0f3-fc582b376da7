https://ieeexplore.ieee.org/document/4380677
描述芯片的系统级行为，整合多种子模型，用于：

信号完整性（SI）分析
电源完整性（PI）联合仿真
芯片-封装-PCB 协同设计
EMC 分析

CSM 通常不是一个单一文件，而是一个包含多个模型文件的文件夹包，类似于 IBIS AMI 的组织方式。

CSM_Model_AI_SoC_2025/
│
├── chip_info.json          # 芯片基本信息
├── power_model.json        # CPM 功耗模型（可复用）
├──
├── IO_Models/
│   ├── CPU_Core_DQ.ibis    # 数据总线 I/O 模型
│   ├── CPU_Core_DQS.ibis   # 数据选通 I/O 模型
│   ├── SerDes_Ch0.ibis     # 高速 SerDes 通道
│   └── SerDes_Ch0.ami      # SerDes 的 AMI 模型（用于统计眼图）
│
├── Package/
│   ├── package_s4p/        # 封装S参数
│   │   ├── DQ_01.s4p
│   │   ├── DQS.s4p
│   │   └── VDD.s4p
│   └── package_rlc.json    # 封装RLC寄生参数（可选）
│
├── Timing/
│   └── timing_constraints.sdf  # 时序约束文件
│
├── EMC/
│   └── noise_sources.json  # 电磁噪声源定义
│
└── csm_manifest.json       # CSM 模型清单文件（描述整个结构）

在 EDA 工具中加载 csm_manifest.json。
工具会自动解析并关联 IBIS、S参数、AMI 等文件。
进行完整的 通道建模（Channel Modeling） 和 端到端仿真。

csm_version: CSM 模型本身的版本号。
chip_name: 关联的芯片型号。
description: 模型用途说明，这里是用于信号和电源完整性分析。
组件 1：功耗模型
    type: "CPM" 表示这是一个 芯片功耗模型。
    path 指向了具体的 CPM 文件 power_model.json，该文件里会包含详细的功耗数据（如动态/静态功耗、DVFS 表等）。
组件 2：DDR5 数据接口
    type: "IBIS" 表示使用 IBIS 标准来描述 I/O 缓冲器的电气行为。
    path 指向 .ibis 文件，定义了 DQ 引脚的驱动强度、上升/下降时间等。
    pins 列出了这个 IBIS 模型对应的物理引脚。
    package_s4p 指向了封装的 S 参数文件，用于仿真信号在封装走线上的传输质量（如反射、损耗）。
组件 3：高速 SerDes 通道
    type: "IBIS-AMI" 是 IBIS 的扩展，专门用于高速串行链路（如 PCIe, USB, Ethernet）。
    它需要两个文件：
    .ibis：基础的 I/O 模型。
    .ami：算法模型接口，描述了发送端（TX）和接收端（RX）的均衡算法（如 FFE, DFE）。
    equalization 字段说明了该通道支持的均衡技术，这对眼图仿真至关重要。
组件 4：电源网络模型
    type: "S-Parameter" 表示这是一个高频网络模型。
    它描述了从芯片内部电源引脚到 PCB 电源平面之间的 阻抗特性。
    用于 电源完整性（PI）分析，比如计算电源噪声（Power Rail Noise）和目标阻抗（Target Impedance）。
default_simulation_setup: 提供仿真的默认条件，确保不同工程师使用一致的起点。