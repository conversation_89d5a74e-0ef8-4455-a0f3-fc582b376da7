https://ieeexplore.ieee.org/document/4387176
描述芯片在不同工作模式下的功耗行为，用于：

电源完整性（PI）分析
功耗预算评估
热设计
低功耗模式验证

power_domains: 定义芯片的电源域及其功耗特性。
operating_modes: 定义不同工作模式下的总功耗。
dvfs_table: 动态调压调频（DVFS）的功耗映射表。
支持温度依赖的静态功耗建模。

model_version: CPM 模型本身的版本号，用于追踪模型迭代和兼容性。

chip_name: 关联的芯片型号，确保模型与具体芯片一一对应。

manufacturer: 芯片制造商信息，用于溯源和管理。

created_date: 模型生成的日期，便于版本控制和团队协作。

notes: 补充说明字段，通常用于标注仿真条件，例如“基于典型工艺角和25°C”。

组件 1：核心电源域（VDD_CORE）
    domain_name: 电源域的名称，与芯片设计中的命名保持一致，如 VDD_CORE 表示处理器核心供电。
    voltage_nominal_V: 该电源域的标称工作电压（单位：伏特），这里是 0.8V。
    voltage_min_V / voltage_max_V: 允许的最低和最高电压范围，用于分析电压波动对功耗的影响。
    static_power_W: 描述该电源域的静态功耗（即漏电功耗），与频率无关。
    typical: 在典型工艺角下的静态功耗值。
    max: 在最坏工艺角下的静态功耗，用于热设计和电源裕量评估。
    temperature_dependency: 静态功耗随温度变化的建模，温度越高，漏电越大，此数据对热仿真至关重要。
    dynamic_power_W: 描述该电源域的动态功耗（即开关功耗），与工作频率强相关。
    at_500MHz / at_1000MHz / at_1500MHz: 在不同频率下的动态功耗数据，用于评估性能与功耗的权衡。
组件 2：I/O 电源域（VDD_IO）
    domain_name: I/O 接口的电源域，通常电压较高（如 1.8V 或 3.3V），以驱动外部信号。
    voltage_nominal_V: I/O 域的标称电压。
    static_power_W: I/O 部分的静态漏电功耗，通常较小。
    dynamic_power_W: I/O 的动态功耗，此处以“每引脚”为单位建模。
    per_pin_4mA: 每个输出驱动为 4mA 的引脚所消耗的动态功耗，便于根据实际负载估算总功耗。
组件 3：工作模式（operating_modes）
    定义芯片在不同使用场景下的整体运行状态和功耗表现。
    mode: 模式名称，如 Normal（正常运行）、Low Power（低功耗）、Sleep（睡眠）等。
    frequency_MHz: 当前模式下的主频。
    active_cores: 激活的核心数量（适用于多核处理器）。
    total_power_W: 该模式下的总功耗，包含核心、缓存、I/O 等所有模块的功耗总和。
    这些模式可用于系统级功耗估算，例如电池续航预测或电源管理策略验证。 
组件 4：动态调压调频表（dvfs_table）
    dvfs_table: 动态电压频率调节（DVFS）的映射表，是低功耗设计的关键。
    voltage_V: 工作电压。
    frequency_MHz: 对应的工作频率。
    power_W: 在该电压和频率组合下的实测或仿真功耗。